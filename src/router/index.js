import { createRouter, createWebHistory } from 'vue-router' 
import { useAuthStore } from '../stores/authStore'

const routes = [
  {
    path: '/',
    component: () => import("../layouts/default/DefaultLayout.vue"),
    children: [
      {
        path: "",
        name: 'AccommodationIndex',
        component: () => import("../views/AccommodationIndex.vue"),
        meta: { requiresAuth: true },
      }
    ]
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import("../views/LoginView.vue"),
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})


// Navigation guard for authentication
router.beforeEach(async (to, _from, next) => {
  const authStore = useAuthStore()
  if (!authStore.ready) await authStore.initializeAuth()

  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  const isAuthenticated = authStore.isAuthenticated

  if (requiresAuth && !isAuthenticated) {
    // Redirect to login if route requires auth and user is not authenticated
    next('/login')
  } else if (to.name === 'Login' && isAuthenticated) {
    // Redirect to home if user is already authenticated and tries to access login
    next('/')
  } else {
    next()
  }
})

export default router