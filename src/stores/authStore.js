import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authService } from '../services/authService'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref(null)
  const loading = ref(false)
  const error = ref(null)
  const ready = ref(false)

  // Getters
  const isAuthenticated = computed(() => {
    return authService.isAuthenticated() && user.value !== null
  })

  const userInfo = computed(() => {
    return user.value
  })

  // Actions
  const initializeAuth = async () => {
    loading.value = true
    error.value = null
    
    try {
      await authService.initialize()
      if (authService.isAuthenticated()) {
        user.value = authService.getUserInfo()
      }
    } catch (err) {
      error.value = err.message
      console.error('Auth initialization failed:', err)
    } finally {
      loading.value = false
      ready.value = true
    }
  }

  const login = async () => {
    loading.value = true
    error.value = null
    
    try {
      await authService.login()
      user.value = authService.getUserInfo()
    } catch (err) {
      error.value = err.message || 'Login failed'
      console.error('Login failed:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const logout = async () => {
    loading.value = true
    error.value = null
    
    try {
      await authService.logout()
      user.value = null
    } catch (err) {
      error.value = err.message || 'Logout failed'
      console.error('Logout failed:', err)
    } finally {
      loading.value = false
    }
  }

  const getAccessToken = async () => {
    try {
      return await authService.getAccessToken()
    } catch (err) {
      error.value = err.message || 'Failed to get access token'
      throw err
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    user,
    loading,
    error,
    ready,
    
    // Getters
    isAuthenticated,
    userInfo,
    
    // Actions
    login,
    logout,
    getAccessToken,
    clearError,
    initializeAuth,
  }
})