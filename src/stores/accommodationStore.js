import { defineStore } from "pinia";
import { ref } from "vue";
import { apiService } from '../services/apiService'

export const useAccommodationStore = defineStore("accommodationStore", () => {
  const fetchingAccommodations = ref(true);
  const accommodations = ref([]);
  const initialized = ref(false);

  const fetchAccommodations = async (append = false) => {
    try {
      fetchingAccommodations.value = true;

      const response = await apiService.get("/Accommodation");

      if (append) {
        accommodations.value.push(...response);
      } else {
        accommodations.value = response;
      }

    } catch (error) {
      console.error("Error fetching data:", error.message);
    } finally {
      fetchingAccommodations.value = false;
    }
  };

  return {
    initialized,
    accommodations,
    fetchingAccommodations,
    fetchAccommodations,
  };
});
